import requests
import urllib.parse
from bs4 import BeautifulSoup
import json
import asyncio

# Import previously converted modules
from src.scrapers.extractor.rapidcloud import RapidCloud
from src.scrapers.extractor.streamsb import StreamSB
from src.scrapers.extractor.streamtape import StreamTape
from src.scrapers.extractor.megacloud import MegaCloud
from src.utils.constants import SRC_BASE_URL, SRC_AJAX_URL, SRC_HOME_URL,SRC_SEARCH_URL,USER_AGENT_HEADER

class HiAnimeError(Exception):
    def __init__(self, message, context, status_code):
        super().__init__(message)
        self.context = context
        self.status_code = status_code

class Logger:
    def info(self, message):
        print(f"INFO: {message}")
log = Logger()

# Enum for AnimeServers
class Servers:
    VidStreaming = "VidStreaming"
    VidCloud = "VidCloud"
    StreamSB = "StreamSB"
    StreamTape = "StreamTape"

# retrieveServerId function from the prompt
def retrieveServerId(soup: BeautifulSoup, index: int, category: str):
    server_item = soup.select_one(f".ps_-block.ps_-block-sub.servers-{category} > .ps__-list .server-item[data-server-id=\"{index}\"]")
    if server_item:
        return server_item.get("data-id")
    return None

async def _getAnimeEpisodeSources(
    episode_id: str,
    server: str = Servers.VidStreaming,
    category: str = "sub"
):
    if episode_id.startswith("http"):
        server_url = episode_id # In Python, we can directly use the string URL
        if server == Servers.VidStreaming or server == Servers.VidCloud:
            # MegaCloud extract2 is async
            extracted_data = await MegaCloud().extract3(server_url)
            return {
                "headers": {"Referer": urllib.parse.urlparse(server_url).scheme + "://" + urllib.parse.urlparse(server_url).netloc + "/"},
                **extracted_data,
            }
        elif server == Servers.StreamSB:
            # StreamSB extract is not async in the converted version, but let's keep it consistent with TS
            sources = StreamSB().extract(server_url, True)
            return {
                "headers": {
                    "Referer": server_url,
                    "watchsb": "streamsb",
                    "User-Agent": USER_AGENT_HEADER,
                },
                "sources": sources,
            }
        elif server == Servers.StreamTape:
            # StreamTape extract is not async in the converted version
            sources = StreamTape().extract(server_url)
            return {
                "headers": {
                    "Referer": server_url,
                    "User-Agent": USER_AGENT_HEADER,
                },
                "sources": sources,
            }
        else: # RapidCloud
            # RapidCloud extract is async
            extracted_data = await RapidCloud().extract(server_url)
            return {
                "headers": {"Referer": server_url},
                **extracted_data,
            }

    ep_id_full_url = f"{SRC_BASE_URL}/watch/{episode_id}"
    log.info(f"EPISODE_ID: {ep_id_full_url}")

    try:
        # Extract the actual episode ID from the URL for the AJAX call
        # Assuming episodeId format is like 'some-anime-title-episode-1?ep=12345'
        ep_id_param = episode_id.split("?ep=")[1] if "?ep=" in episode_id else episode_id

        resp = requests.get(
            f"{SRC_AJAX_URL}/v2/episode/servers?episodeId={ep_id_param}",
            headers={
                "Referer": ep_id_full_url,
                "X-Requested-With": "XMLHttpRequest",
            }
        )
        resp.raise_for_status()
        soup = BeautifulSoup(resp.json()["html"], 'html.parser')

        server_id = None

        log.info(f"THE SERVER: {server}")

        if server == Servers.VidCloud:
            server_id = retrieveServerId(soup, 1, category)
            if not server_id: raise Exception("RapidCloud not found")
        elif server == Servers.VidStreaming:
            server_id = retrieveServerId(soup, 4, category)
            log.info(f"SERVER_ID: {server_id}")
            if not server_id: raise Exception("VidStreaming not found")
        elif server == Servers.StreamSB:
            server_id = retrieveServerId(soup, 5, category)
            if not server_id: raise Exception("StreamSB not found")
        elif server == Servers.StreamTape:
            server_id = retrieveServerId(soup, 3, category)
            if not server_id: raise Exception("StreamTape not found")

        # Fetch sources link
        sources_resp = requests.get(
            f"{SRC_AJAX_URL}/v2/episode/sources?id={server_id}"
        )
        sources_resp.raise_for_status()
        link = sources_resp.json()["link"]
        log.info(f"THE LINK: {link}")

        return await _getAnimeEpisodeSources(link, server, category)

    except requests.exceptions.RequestException as e:
        raise HiAnimeError.wrapError(e, "_getAnimeEpisodeSources")
    except Exception as err:
        raise HiAnimeError.wrapError(err, "_getAnimeEpisodeSources")


async def getAnimeEpisodeSources(
    episode_id: str,
    server: str,
    category: str
):
    if not episode_id or "?ep=" not in episode_id:
        raise HiAnimeError(
            "invalid anime episode id",
            "getAnimeEpisodeSources",
            400
        )
    if not category.strip():
        raise HiAnimeError(
            "invalid anime episode category",
            "getAnimeEpisodeSources",
            400
        )

    mal_id = None
    anilist_id = None

    anime_url = f"{SRC_BASE_URL}/watch/{episode_id.split('?ep=')[0]}"

    try:
        # Concurrently fetch episode sources and anime page data
        episode_src_data_task = _getAnimeEpisodeSources(episode_id, server, category)
        anime_src_req_task = asyncio.to_thread(requests.get, anime_url, headers={
            "Referer": SRC_BASE_URL,
            "User-Agent": USER_AGENT_HEADER,
            "X-Requested-With": "XMLHttpRequest",
        })

        episode_src_data, anime_src_resp = await asyncio.gather(
            episode_src_data_task, anime_src_req_task
        )
        anime_src_resp.raise_for_status()

        log.info(f"EPISODE_SRC_DATA: {json.dumps(episode_src_data)}")

        soup = BeautifulSoup(anime_src_resp.text, 'html.parser')

        try:
            sync_data_script = soup.find("script", id="syncData")
            if sync_data_script and sync_data_script.string:
                sync_data = json.loads(sync_data_script.string)
                anilist_id = int(sync_data.get("anilist_id")) if sync_data.get("anilist_id") else None
                mal_id = int(sync_data.get("mal_id")) if sync_data.get("mal_id") else None
        except Exception as err:
            log.info(f"Error parsing syncData: {err}")
            anilist_id = None
            mal_id = None

        episode_src_data["anilistID"] = anilist_id
        episode_src_data["malID"] = mal_id

        return episode_src_data

    except requests.exceptions.RequestException as e:
        raise HiAnimeError.wrapError(e, "getAnimeEpisodeSources")
    except Exception as err:
        raise HiAnimeError.wrapError(err, "getAnimeEpisodeSources")

# Helper for HiAnimeError to wrap errors (assuming it's a static method)
# If HiAnimeError is a class, this needs to be a method of the class
# For now, let's define it as a standalone function or ensure it's part of the class
if not hasattr(HiAnimeError, 'wrapError'):
    def _wrap_error(err, context):
        if isinstance(err, HiAnimeError):
            return err
        return HiAnimeError(str(err), context, 500) # Default to 500 for unknown errors
    HiAnimeError.wrapError = staticmethod(_wrap_error)

# # Example Usage
# async def main():
#     # Example usage (replace with actual episode ID and server)
#     episode_id = "attack-on-titan-112?ep=3303"
#     server = Servers.VidStreaming
#     category = "sub"

#     try:
#         sources = await getAnimeEpisodeSources(episode_id, server, category)
#         print(json.dumps(sources, indent=4))
#     except HiAnimeError as e:
#         print(f"HiAnimeError: {e.context} (Status: {e.status_code})")
#     except Exception as e:
#         print(f"An unexpected error occurred: {e}")

# if __name__ == "__main__":
#     asyncio.run(main())


